-- =====================================================
-- SETUP SITE SETTINGS FUNCTION
-- =====================================================
-- Run this SQL script in your Supabase SQL Editor to create the missing update_site_setting function

-- Drop any existing versions of the function to avoid conflicts
DROP FUNCTION IF EXISTS update_site_setting CASCADE;
DROP FUNCTION IF EXISTS public.update_site_setting CASCADE;

-- Create the update_site_setting function with correct signature
CREATE OR REPLACE FUNCTION update_site_setting(
    key_name TEXT,
    new_value TEXT DEFAULT NULL,
    new_cloudinary_url TEXT DEFAULT NULL,
    new_cloudinary_public_id TEXT DEFAULT NULL,
    new_media_asset_id UUID DEFAULT NULL,
    user_id UUID DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    UPDATE site_settings
    SET
        setting_value = COALESCE(new_value, setting_value),
        cloudinary_url = COALESCE(new_cloudinary_url, cloudinary_url),
        cloudinary_public_id = COALESCE(new_cloudinary_public_id, cloudinary_public_id),
        media_asset_id = COALESCE(new_media_asset_id, media_asset_id),
        updated_at = NOW(),
        updated_by = user_id
    WHERE setting_key = key_name;

    -- If no rows were updated, insert a new setting
    IF NOT FOUND THEN
        INSERT INTO site_settings (
            setting_key,
            setting_value,
            setting_type,
            cloudinary_url,
            cloudinary_public_id,
            media_asset_id,
            created_by,
            updated_by,
            description
        ) VALUES (
            key_name,
            new_value,
            CASE 
                WHEN new_cloudinary_url IS NOT NULL THEN 'image'
                ELSE 'string'
            END,
            new_cloudinary_url,
            new_cloudinary_public_id,
            new_media_asset_id,
            user_id,
            user_id,
            'Auto-created setting for ' || key_name
        );
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions to authenticated users
GRANT EXECUTE ON FUNCTION update_site_setting(TEXT, TEXT, TEXT, TEXT, UUID, UUID) TO authenticated;

-- Test the function (optional - you can remove this)
-- SELECT update_site_setting('test_setting', 'test_value', NULL, NULL, NULL, NULL);

-- Verify the function exists
SELECT 
    routine_name,
    routine_type,
    data_type
FROM information_schema.routines 
WHERE routine_name = 'update_site_setting' 
AND routine_schema = 'public';
