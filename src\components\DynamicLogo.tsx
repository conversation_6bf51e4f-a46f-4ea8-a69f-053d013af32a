/**
 * Dynamic Logo Component
 * 
 * A component that displays the site logo from the database settings.
 * Falls back to the default static logo if no custom logo is set.
 * Integrates with the site settings system for centralized logo management.
 */

'use client'

import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import { useSiteSettings } from '@/lib/redux/hooks'

interface DynamicLogoProps {
  width?: number
  height?: number
  className?: string
  alt?: string
  fallbackSrc?: string
  priority?: boolean
}

export default function DynamicLogo({
  width = 48,
  height = 48,
  className = "object-contain",
  alt = "LGU Ipil Logo",
  fallbackSrc = "/images/logo.png",
  priority = false
}: DynamicLogoProps) {
  const { getSettingUrl, loading } = useSiteSettings()
  const [logoSrc, setLogoSrc] = useState<string>(fallbackSrc)
  const [imageError, setImageError] = useState(false)

  useEffect(() => {
    if (!loading) {
      const customLogo = getSettingUrl('site_logo')
      if (customLogo && customLogo !== logoSrc) {
        setLogoSrc(customLogo)
        setImageError(false)
      }
    }
  }, [loading, getSettingUrl, logoSrc])

  const handleImageError = () => {
    if (logoSrc !== fallbackSrc) {
      setImageError(true)
      setLogoSrc(fallbackSrc)
    }
  }

  return (
    <Image
      src={logoSrc}
      alt={alt}
      width={width}
      height={height}
      className={className}
      priority={priority}
      onError={handleImageError}
    />
  )
}

/**
 * Dynamic Logo with Text Component
 * 
 * Displays the logo alongside site name and tagline from settings.
 */
interface DynamicLogoWithTextProps extends DynamicLogoProps {
  showText?: boolean
  siteName?: string
  siteTagline?: string
  textClassName?: string
  containerClassName?: string
}

export function DynamicLogoWithText({
  showText = true,
  siteName,
  siteTagline,
  textClassName = "",
  containerClassName = "flex items-center space-x-3",
  ...logoProps
}: DynamicLogoWithTextProps) {
  const { getSetting, loading } = useSiteSettings()

  const displayName = siteName || getSetting('site_name', 'LGU Ipil')
  const displayTagline = siteTagline || getSetting('site_tagline', 'Local Gov')

  return (
    <div className={containerClassName}>
      <div className="flex items-center justify-center relative">
        <DynamicLogo {...logoProps} />
      </div>
      {showText && (
        <div className={textClassName}>
          <h1 className="text-xl font-bold text-gray-900">{displayName}</h1>
          <p className="text-sm font-medium" style={{ color: '#0c035f' }}>
            {displayTagline}
          </p>
        </div>
      )}
    </div>
  )
}

/**
 * Professional Hero Background Skeleton
 *
 * YouTube-style skeleton screen that displays while the background loads.
 * Eliminates unprofessional FOUC and provides smooth loading experience.
 */
interface HeroBackgroundSkeletonProps {
  className?: string
  children?: React.ReactNode
}

function HeroBackgroundSkeleton({
  className = "w-full h-full",
  children
}: HeroBackgroundSkeletonProps) {
  return (
    <div className={`${className} relative overflow-hidden`}>
      {/* Professional gradient skeleton background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-200 via-gray-100 to-slate-300 animate-pulse">
        {/* Subtle shimmer effect like YouTube */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer"></div>

        {/* Geometric pattern overlay for visual interest */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-white/30 rounded-full blur-xl"></div>
          <div className="absolute top-3/4 right-1/4 w-24 h-24 bg-white/20 rounded-full blur-lg"></div>
          <div className="absolute bottom-1/4 left-1/3 w-16 h-16 bg-white/25 rounded-full blur-md"></div>
        </div>
      </div>

      {/* Content overlay */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  )
}

/**
 * Dynamic Hero Background Component
 *
 * Professional hero background with YouTube-style skeleton loading.
 * Eliminates FOUC and provides enterprise-level UX.
 */
interface DynamicHeroBackgroundProps {
  className?: string
  fallbackSrc?: string
  children?: React.ReactNode
}

export function DynamicHeroBackground({
  className = "w-full h-full bg-cover bg-center bg-no-repeat",
  fallbackSrc = "/images/lgu-ipil.png",
  children
}: DynamicHeroBackgroundProps) {
  const { getSettingUrl, serverInitialized, loading } = useSiteSettings()
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)

  // Get the background URL from database
  const databaseImageUrl = getSettingUrl('hero_background')
  const backgroundImageUrl = databaseImageUrl || fallbackSrc

  // Preload the background image
  useEffect(() => {
    if (backgroundImageUrl && serverInitialized) {
      const img = new Image()
      img.onload = () => {
        setImageLoaded(true)
        console.log('[DynamicHeroBackground] ✅ Background image loaded successfully')
      }
      img.onerror = () => {
        setImageError(true)
        console.warn('[DynamicHeroBackground] ⚠️ Background image failed to load')
      }
      img.src = backgroundImageUrl
    }
  }, [backgroundImageUrl, serverInitialized])

  // Show skeleton while loading or if server not initialized
  const showSkeleton = !serverInitialized || loading || (!imageLoaded && !imageError)

  if (showSkeleton) {
    console.log('[DynamicHeroBackground] 🎭 Showing professional skeleton screen')
    return (
      <HeroBackgroundSkeleton className={className}>
        {children}
      </HeroBackgroundSkeleton>
    )
  }

  // Show the actual background once loaded
  console.log('[DynamicHeroBackground] 🏢 Showing loaded background:', databaseImageUrl ? 'DATABASE image' : 'FALLBACK image')

  return (
    <div
      className={`${className} transition-opacity duration-300 ease-in-out`}
      style={{
        backgroundImage: `url("${backgroundImageUrl}")`,
      }}
    >
      {children}
    </div>
  )
}
