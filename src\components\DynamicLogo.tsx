/**
 * Dynamic Logo Component
 * 
 * A component that displays the site logo from the database settings.
 * Falls back to the default static logo if no custom logo is set.
 * Integrates with the site settings system for centralized logo management.
 */

'use client'

import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import { useSiteSettings } from '@/lib/redux/hooks'

interface DynamicLogoProps {
  width?: number
  height?: number
  className?: string
  alt?: string
  fallbackSrc?: string
  priority?: boolean
}

export default function DynamicLogo({
  width = 48,
  height = 48,
  className = "object-contain",
  alt = "LGU Ipil Logo",
  fallbackSrc = "/images/logo.png",
  priority = false
}: DynamicLogoProps) {
  const { getSettingUrl, loading } = useSiteSettings()
  const [logoSrc, setLogoSrc] = useState<string>(fallbackSrc)
  const [imageError, setImageError] = useState(false)

  useEffect(() => {
    if (!loading) {
      const customLogo = getSettingUrl('site_logo')
      if (customLogo && customLogo !== logoSrc) {
        setLogoSrc(customLogo)
        setImageError(false)
      }
    }
  }, [loading, getSettingUrl, logoSrc])

  const handleImageError = () => {
    if (logoSrc !== fallbackSrc) {
      setImageError(true)
      setLogoSrc(fallbackSrc)
    }
  }

  return (
    <Image
      src={logoSrc}
      alt={alt}
      width={width}
      height={height}
      className={className}
      priority={priority}
      onError={handleImageError}
    />
  )
}

/**
 * Dynamic Logo with Text Component
 * 
 * Displays the logo alongside site name and tagline from settings.
 */
interface DynamicLogoWithTextProps extends DynamicLogoProps {
  showText?: boolean
  siteName?: string
  siteTagline?: string
  textClassName?: string
  containerClassName?: string
}

export function DynamicLogoWithText({
  showText = true,
  siteName,
  siteTagline,
  textClassName = "",
  containerClassName = "flex items-center space-x-3",
  ...logoProps
}: DynamicLogoWithTextProps) {
  const { getSetting, loading } = useSiteSettings()

  const displayName = siteName || getSetting('site_name', 'LGU Ipil')
  const displayTagline = siteTagline || getSetting('site_tagline', 'Local Gov')

  return (
    <div className={containerClassName}>
      <div className="flex items-center justify-center relative">
        <DynamicLogo {...logoProps} />
      </div>
      {showText && (
        <div className={textClassName}>
          <h1 className="text-xl font-bold text-gray-900">{displayName}</h1>
          <p className="text-sm font-medium" style={{ color: '#0c035f' }}>
            {displayTagline}
          </p>
        </div>
      )}
    </div>
  )
}

/**
 * Dynamic Hero Background Component
 *
 * Displays the hero background image from settings.
 */
interface DynamicHeroBackgroundProps {
  className?: string
  fallbackSrc?: string
  children?: React.ReactNode
}

export function DynamicHeroBackground({
  className = "w-full h-full bg-cover bg-center bg-no-repeat",
  fallbackSrc = "/images/lgu-ipil.png",
  children
}: DynamicHeroBackgroundProps) {
  const { getSettingUrl, loading, settings } = useSiteSettings()

  // 🎯 FIXED: Always prioritize database image over fallback
  // The getSettingUrl function handles the priority: cloudinary_url > value > fallback
  const backgroundImageUrl = getSettingUrl('hero_background', fallbackSrc)

  console.log('[DynamicHeroBackground] 🎨 Database setting:', settings.hero_background)
  console.log('[DynamicHeroBackground] 🎨 Final Background URL:', backgroundImageUrl)
  console.log('[DynamicHeroBackground] 🔄 Loading state:', loading)

  return (
    <div
      className={className}
      style={{
        backgroundImage: `url("${backgroundImageUrl}")`,
      }}
    >
      {children}
    </div>
  )
}
