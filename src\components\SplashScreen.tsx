/**
 * Professional Facebook-Style Splash Screen
 * 
 * Enterprise-level splash screen that appears during full page reloads,
 * providing a smooth, professional loading experience similar to Facebook.
 * Tailored for LGU branding with government-appropriate colors and messaging.
 */

'use client'

import { useEffect, useState } from 'react'
import { useSiteSettings } from '@/lib/redux/hooks'
import Image from 'next/image'

interface SplashScreenProps {
  onComplete?: () => void
  minDisplayTime?: number
}

export default function SplashScreen({ 
  onComplete, 
  minDisplayTime = 2000 
}: SplashScreenProps) {
  const [isVisible, setIsVisible] = useState(true)
  const [loadingProgress, setLoadingProgress] = useState(0)
  const [loadingText, setLoadingText] = useState('Loading your experience...')
  const { getSetting, getSettingUrl, serverInitialized } = useSiteSettings()

  // Get branding information
  const siteName = getSetting('site_name', 'LGU Ipil')
  const siteTagline = getSetting('site_tagline', 'Local Government Unit')
  const logoUrl = getSettingUrl('site_logo', '/images/logo.png')

  // Loading progress simulation
  useEffect(() => {
    const progressInterval = setInterval(() => {
      setLoadingProgress(prev => {
        if (prev >= 100) {
          clearInterval(progressInterval)
          return 100
        }
        return prev + Math.random() * 15
      })
    }, 150)

    return () => clearInterval(progressInterval)
  }, [])

  // Loading text rotation
  useEffect(() => {
    const loadingMessages = [
      'Loading your experience...',
      'Preparing government services...',
      'Connecting to local systems...',
      'Almost ready...'
    ]

    let messageIndex = 0
    const textInterval = setInterval(() => {
      messageIndex = (messageIndex + 1) % loadingMessages.length
      setLoadingText(loadingMessages[messageIndex])
    }, 1500)

    return () => clearInterval(textInterval)
  }, [])

  // Handle splash screen completion
  useEffect(() => {
    if (serverInitialized && loadingProgress >= 100) {
      const timer = setTimeout(() => {
        setIsVisible(false)
        setTimeout(() => {
          onComplete?.()
        }, 500) // Wait for fade out animation
      }, Math.max(0, minDisplayTime - Date.now()))

      return () => clearTimeout(timer)
    }
  }, [serverInitialized, loadingProgress, minDisplayTime, onComplete])

  if (!isVisible) return null

  return (
    <div
      className={`fixed inset-0 z-[9999] flex items-center justify-center transition-all duration-500 ${
        isVisible ? 'opacity-100 splash-fade-in' : 'opacity-0 splash-fade-out'
      }`}
      style={{
        background: 'linear-gradient(135deg, #0c035f 0%, #1e1b4b 50%, #312e81 100%)'
      }}
    >
      {/* Stable Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-white/10 rounded-full blur-2xl"></div>
        <div className="absolute top-3/4 right-1/4 w-24 h-24 bg-white/8 rounded-full blur-xl"></div>
        <div className="absolute bottom-1/4 left-1/3 w-16 h-16 bg-white/12 rounded-full blur-lg"></div>
        <div className="absolute top-1/2 right-1/2 w-20 h-20 bg-white/6 rounded-full blur-xl"></div>
      </div>

      {/* Main Content */}
      <div className="text-center space-y-8 px-6 max-w-md mx-auto">
        {/* Logo */}
        <div className="flex justify-center">
          <div className="relative">
            {/* Logo Background Circle */}
            <div className="w-32 h-32 rounded-full bg-white/95 backdrop-blur-sm flex items-center justify-center shadow-2xl splash-logo-pulse">
              <Image
                src={logoUrl}
                alt={`${siteName} Logo`}
                width={80}
                height={80}
                className="object-contain"
                priority
              />
            </div>

            {/* Pulsing Ring */}
            <div className="absolute inset-0 rounded-full border-2 border-white/40 animate-ping"></div>
          </div>
        </div>

        {/* Site Name */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold text-white tracking-tight splash-text-glow">
            {siteName}
          </h1>
          <p className="text-lg text-slate-300 font-medium">
            {siteTagline}
          </p>
        </div>

        {/* Loading Animation */}
        <div className="space-y-4">
          {/* Loading Dots */}
          <div className="flex justify-center space-x-2">
            <div 
              className="w-3 h-3 rounded-full animate-bounce"
              style={{ 
                backgroundColor: '#e01e09',
                animationDelay: '0ms'
              }}
            ></div>
            <div 
              className="w-3 h-3 rounded-full animate-bounce"
              style={{ 
                backgroundColor: '#e01e09',
                animationDelay: '150ms'
              }}
            ></div>
            <div 
              className="w-3 h-3 rounded-full animate-bounce"
              style={{ 
                backgroundColor: '#e01e09',
                animationDelay: '300ms'
              }}
            ></div>
          </div>

          {/* Loading Text */}
          <p className="text-slate-400 text-sm font-medium transition-all duration-300">
            {loadingText}
          </p>
        </div>

        {/* Progress Bar */}
        <div className="w-full max-w-xs mx-auto">
          <div className="bg-slate-700/50 rounded-full h-1 overflow-hidden">
            <div 
              className="h-full rounded-full transition-all duration-300 ease-out"
              style={{
                width: `${loadingProgress}%`,
                background: 'linear-gradient(90deg, #0c035f 0%, #e01e09 100%)'
              }}
            ></div>
          </div>
        </div>

        {/* Footer */}
        <div className="pt-8 space-y-2">
          <p className="text-xs text-slate-500">from</p>
          <p className="text-sm font-semibold text-slate-400">
            {siteName} Development Team
          </p>
        </div>
      </div>

      {/* Subtle Grid Pattern */}
      <div 
        className="absolute inset-0 opacity-5"
        style={{
          backgroundImage: `
            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)
          `,
          backgroundSize: '50px 50px'
        }}
      ></div>
    </div>
  )
}

/**
 * Hook to manage splash screen state
 */
export function useSplashScreen() {
  const [showSplash, setShowSplash] = useState(true)
  const [isFirstLoad, setIsFirstLoad] = useState(true)

  useEffect(() => {
    // Check if this is a full page reload
    const isPageReload = performance.navigation?.type === 1 || 
                        performance.getEntriesByType('navigation')[0]?.type === 'reload'
    
    if (!isPageReload) {
      setShowSplash(false)
      setIsFirstLoad(false)
    }
  }, [])

  const hideSplash = () => {
    setShowSplash(false)
    setIsFirstLoad(false)
  }

  return {
    showSplash: showSplash && isFirstLoad,
    hideSplash
  }
}
