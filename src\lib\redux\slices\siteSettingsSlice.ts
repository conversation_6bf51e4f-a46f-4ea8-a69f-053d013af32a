/**
 * Site Settings Redux Slice
 * 
 * Manages site-specific settings including:
 * - Site logo and branding
 * - Hero background images
 * - Site name and tagline
 * - Cloudinary integration for media
 * - Database persistence with error handling
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit'

// Types
export interface SiteSetting {
  value: string
  cloudinary_url: string | null
  cloudinary_public_id: string | null
  type: 'string' | 'image' | 'url' | 'text'
  description: string
}

export interface SiteSettings {
  site_logo: SiteSetting
  hero_background: SiteSetting
  site_name: SiteSetting
  site_tagline: SiteSetting
  [key: string]: SiteSetting
}

export interface SiteSettingsState {
  settings: SiteSettings
  loading: boolean
  error: string | null
  lastUpdated: string | null
  initialized: boolean
  serverInitialized: boolean // Track if initialized by server-side loading
  uploadingSettings: { [key: string]: boolean }
}

// Default settings
const defaultSettings: SiteSettings = {
  site_logo: {
    value: '/images/logo.png',
    cloudinary_url: null,
    cloudinary_public_id: null,
    type: 'image',
    description: 'Main site logo'
  },
  hero_background: {
    value: '/images/lgu-ipil.png',
    cloudinary_url: null,
    cloudinary_public_id: null,
    type: 'image',
    description: 'Hero background image'
  },
  site_name: {
    value: 'LGU Ipil',
    cloudinary_url: null,
    cloudinary_public_id: null,
    type: 'string',
    description: 'Site name'
  },
  site_tagline: {
    value: 'Local Government Unit',
    cloudinary_url: null,
    cloudinary_public_id: null,
    type: 'string',
    description: 'Site tagline'
  }
}

// Initial state
const initialState: SiteSettingsState = {
  settings: defaultSettings,
  loading: false,
  error: null,
  lastUpdated: null,
  initialized: false,
  serverInitialized: false,
  uploadingSettings: {}
}

// API Response types
interface ApiResponse<T = any> {
  success?: boolean
  data?: T
  settings?: SiteSettings
  error?: string
  message?: string
  details?: string
  code?: string
}

// Async thunks
export const loadSiteSettings = createAsyncThunk(
  'siteSettings/loadSiteSettings',
  async (_, { rejectWithValue }) => {
    try {
      console.log('[Redux] Loading site settings...')
      
      const response = await fetch('/api/site-settings', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      let responseData: ApiResponse
      try {
        const responseText = await response.text()
        responseData = responseText ? JSON.parse(responseText) : {}
      } catch (parseError) {
        console.warn('[Redux] Failed to parse response:', parseError)
        responseData = { error: 'Failed to parse response' }
      }

      console.log('[Redux] Load response:', { status: response.status, data: responseData })

      if (!response.ok) {
        // If it's a database error (table doesn't exist), use defaults but don't error
        if (responseData.code === '42P01' || responseData.error?.includes('does not exist')) {
          console.warn('[Redux] Database table does not exist, using default settings')
          return { settings: defaultSettings, usingDefaults: true }
        }
        
        return rejectWithValue(responseData.error || responseData.message || `HTTP ${response.status}`)
      }

      const settings = responseData.settings || responseData.data || defaultSettings
      return { settings, usingDefaults: false }

    } catch (error) {
      console.error('[Redux] Load site settings error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to load site settings'
      return rejectWithValue(errorMessage)
    }
  }
)

export const updateSiteSetting = createAsyncThunk(
  'siteSettings/updateSiteSetting',
  async (
    params: {
      key: string
      value: string
      cloudinaryUrl?: string
      cloudinaryPublicId?: string
    },
    { rejectWithValue, getState }
  ) => {
    try {
      const { key, value, cloudinaryUrl, cloudinaryPublicId } = params
      console.log('[Redux] Updating site setting:', params)

      const response = await fetch('/api/site-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          setting_key: key,
          setting_value: value,
          cloudinary_url: cloudinaryUrl,
          cloudinary_public_id: cloudinaryPublicId
        })
      })

      let responseData: ApiResponse
      try {
        const responseText = await response.text()
        responseData = responseText ? JSON.parse(responseText) : {}
      } catch (parseError) {
        console.warn('[Redux] Failed to parse update response:', parseError)
        responseData = { error: 'Failed to parse response' }
      }

      console.log('[Redux] Update response:', { status: response.status, data: responseData })

      if (!response.ok) {
        return rejectWithValue(responseData.error || responseData.message || `HTTP ${response.status}`)
      }

      // Return the updated setting data
      return {
        key,
        setting: {
          value,
          cloudinary_url: cloudinaryUrl || null,
          cloudinary_public_id: cloudinaryPublicId || null,
          type: (getState() as any).siteSettings.settings[key]?.type || 'string',
          description: (getState() as any).siteSettings.settings[key]?.description || ''
        } as SiteSetting
      }

    } catch (error) {
      console.error('[Redux] Update site setting error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to update site setting'
      return rejectWithValue(errorMessage)
    }
  }
)

export const uploadSiteSettingFile = createAsyncThunk(
  'siteSettings/uploadSiteSettingFile',
  async (
    params: {
      key: string
      file: File
      type: 'logo' | 'background' | 'favicon'
    },
    { rejectWithValue, dispatch }
  ) => {
    try {
      const { key, file, type } = params
      console.log('[Redux] Uploading file for setting:', { key, fileName: file.name, type })

      // Set uploading state
      dispatch(setUploadingState({ key, uploading: true }))

      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', type)
      formData.append('key', key)

      const response = await fetch('/api/site-settings/upload', {
        method: 'POST',
        body: formData
      })

      let responseData: ApiResponse
      try {
        const responseText = await response.text()
        responseData = responseText ? JSON.parse(responseText) : {}
      } catch (parseError) {
        console.warn('[Redux] Failed to parse upload response:', parseError)
        responseData = { error: 'Failed to parse response' }
      }

      console.log('[Redux] Upload response:', { status: response.status, data: responseData })

      if (!response.ok) {
        return rejectWithValue(responseData.error || responseData.message || `HTTP ${response.status}`)
      }

      // If upload was successful, update the setting
      if (responseData.cloudinary_url) {
        await dispatch(updateSiteSetting({
          key,
          value: responseData.cloudinary_url,
          cloudinaryUrl: responseData.cloudinary_url,
          cloudinaryPublicId: responseData.cloudinary_public_id
        }))
      }

      return {
        key,
        cloudinary_url: responseData.cloudinary_url,
        cloudinary_public_id: responseData.cloudinary_public_id
      }

    } catch (error) {
      console.error('[Redux] Upload file error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload file'
      return rejectWithValue(errorMessage)
    } finally {
      // Clear uploading state
      dispatch(setUploadingState({ key: params.key, uploading: false }))
    }
  }
)

export const updateMultipleSiteSettings = createAsyncThunk(
  'siteSettings/updateMultipleSiteSettings',
  async (
    settings: Record<string, Partial<SiteSetting>>,
    { rejectWithValue, dispatch }
  ) => {
    try {
      console.log('[Redux] Updating multiple site settings:', settings)

      const results: { [key: string]: SiteSetting } = {}

      // Update each setting individually
      for (const [key, settingData] of Object.entries(settings)) {
        if (settingData.value !== undefined) {
          const result = await dispatch(updateSiteSetting({
            key,
            value: settingData.value,
            cloudinaryUrl: settingData.cloudinary_url,
            cloudinaryPublicId: settingData.cloudinary_public_id
          }))

          if (updateSiteSetting.fulfilled.match(result)) {
            results[key] = result.payload.setting
          }
        }
      }

      return results

    } catch (error) {
      console.error('[Redux] Update multiple settings error:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to update multiple settings'
      return rejectWithValue(errorMessage)
    }
  }
)

// Site Settings slice
const siteSettingsSlice = createSlice({
  name: 'siteSettings',
  initialState,
  reducers: {
    clearSiteSettingsError: (state) => {
      state.error = null
    },
    setUploadingState: (state, action: PayloadAction<{ key: string; uploading: boolean }>) => {
      const { key, uploading } = action.payload
      state.uploadingSettings[key] = uploading
    },
    resetSiteSettings: () => {
      return initialState
    },
    setSiteSetting: (state, action: PayloadAction<{ key: string; setting: SiteSetting }>) => {
      const { key, setting } = action.payload
      state.settings[key] = setting
      state.lastUpdated = new Date().toISOString()
    },
    initializeSiteSettings: (state, action: PayloadAction<SiteSettings>) => {
      state.settings = { ...defaultSettings, ...action.payload }
      state.initialized = true
      state.serverInitialized = true // Mark as server-initialized
      state.loading = false
      state.lastUpdated = new Date().toISOString()
    }
  },
  extraReducers: (builder) => {
    // Load site settings
    builder
      .addCase(loadSiteSettings.pending, (state) => {
        state.loading = true
        state.error = null
      })
      .addCase(loadSiteSettings.fulfilled, (state, action) => {
        state.loading = false
        state.settings = { ...defaultSettings, ...action.payload.settings }
        state.initialized = true
        state.lastUpdated = new Date().toISOString()
        state.error = null

        if (action.payload.usingDefaults) {
          console.log('[Redux] Using default settings due to database unavailability')
        }
      })
      .addCase(loadSiteSettings.rejected, (state, action) => {
        state.loading = false
        state.error = action.payload as string
        // Keep default settings even on error
        state.settings = defaultSettings
        state.initialized = true
      })

    // Update site setting
    builder
      .addCase(updateSiteSetting.pending, (state) => {
        state.error = null
      })
      .addCase(updateSiteSetting.fulfilled, (state, action) => {
        const { key, setting } = action.payload
        state.settings[key] = setting
        state.lastUpdated = new Date().toISOString()
        state.error = null
      })
      .addCase(updateSiteSetting.rejected, (state, action) => {
        state.error = action.payload as string
      })

    // Upload file
    builder
      .addCase(uploadSiteSettingFile.pending, (state, action) => {
        const key = action.meta.arg.key
        state.uploadingSettings[key] = true
        state.error = null
      })
      .addCase(uploadSiteSettingFile.fulfilled, (state, action) => {
        const key = action.payload.key
        state.uploadingSettings[key] = false
        state.error = null
        // The actual setting update is handled by the nested updateSiteSetting call
      })
      .addCase(uploadSiteSettingFile.rejected, (state, action) => {
        const key = action.meta.arg.key
        state.uploadingSettings[key] = false
        state.error = action.payload as string
      })

    // Update multiple settings
    builder
      .addCase(updateMultipleSiteSettings.pending, (state) => {
        state.error = null
      })
      .addCase(updateMultipleSiteSettings.fulfilled, (state, action) => {
        Object.entries(action.payload).forEach(([key, setting]) => {
          state.settings[key] = setting
        })
        state.lastUpdated = new Date().toISOString()
        state.error = null
      })
      .addCase(updateMultipleSiteSettings.rejected, (state, action) => {
        state.error = action.payload as string
      })
  }
})

// Export actions
export const {
  clearSiteSettingsError,
  setUploadingState,
  resetSiteSettings,
  setSiteSetting,
  initializeSiteSettings
} = siteSettingsSlice.actions

// Selectors
export const selectSiteSettings = (state: { siteSettings: SiteSettingsState }) => state.siteSettings.settings
export const selectSiteSettingsLoading = (state: { siteSettings: SiteSettingsState }) => state.siteSettings.loading
export const selectSiteSettingsError = (state: { siteSettings: SiteSettingsState }) => state.siteSettings.error
export const selectSiteSettingsInitialized = (state: { siteSettings: SiteSettingsState }) => state.siteSettings.initialized
export const selectSiteSettingsServerInitialized = (state: { siteSettings: SiteSettingsState }) => state.siteSettings.serverInitialized
export const selectSiteSettingsLastUpdated = (state: { siteSettings: SiteSettingsState }) => state.siteSettings.lastUpdated
export const selectUploadingSettings = (state: { siteSettings: SiteSettingsState }) => state.siteSettings.uploadingSettings

// Specific setting selectors
export const selectSiteSetting = (key: string) => (state: { siteSettings: SiteSettingsState }) =>
  state.siteSettings.settings[key]

export const selectSiteSettingValue = (key: string, fallback?: string) => (state: { siteSettings: SiteSettingsState }) =>
  state.siteSettings.settings[key]?.value || fallback || null

export const selectSiteSettingUrl = (key: string, fallback?: string) => (state: { siteSettings: SiteSettingsState }) => {
  const setting = state.siteSettings.settings[key]

  // 🏢 ENTERPRISE APPROACH: Complete replacement - no fallback when database has value
  // If database has ANY value (cloudinary_url OR value), use it exclusively
  if (setting?.cloudinary_url || setting?.value) {
    return setting.cloudinary_url || setting.value || ''
  }

  // Only use fallback if database has NO value at all (new/empty state)
  return fallback || null
}

export const selectIsSettingUploading = (key: string) => (state: { siteSettings: SiteSettingsState }) =>
  state.siteSettings.uploadingSettings[key] || false

// Commonly used settings
export const selectSiteLogo = (state: { siteSettings: SiteSettingsState }) =>
  state.siteSettings.settings.site_logo

export const selectHeroBackground = (state: { siteSettings: SiteSettingsState }) =>
  state.siteSettings.settings.hero_background

export const selectSiteName = (state: { siteSettings: SiteSettingsState }) =>
  state.siteSettings.settings.site_name?.value || 'LGU Ipil'

export const selectSiteTagline = (state: { siteSettings: SiteSettingsState }) =>
  state.siteSettings.settings.site_tagline?.value || 'Local Government Unit'

// Export slice and reducer
export { siteSettingsSlice }
export default siteSettingsSlice.reducer
