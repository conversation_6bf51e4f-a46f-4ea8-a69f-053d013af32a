/**
 * Enterprise-level Hero Background Styles
 * 
 * Optimized CSS for eliminating FOUC and providing smooth transitions
 * between background images. Used by enterprise platforms like Facebook.
 */

/* Base hero background container */
.hero-background {
  position: relative;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
}

/* Enterprise-level smooth transition for background changes */
.hero-background-transition {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: background-image, opacity, filter, transform;
  backface-visibility: hidden;
  transform: translateZ(0); /* Force GPU acceleration */
}

/* Loading state with subtle blur */
.hero-background-loading {
  filter: blur(1px);
  opacity: 0.9;
}

/* Preload optimization */
.hero-background-preload {
  position: absolute;
  top: -9999px;
  left: -9999px;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}

/* Enterprise-level fade transition */
.hero-background-fade-enter {
  opacity: 0;
  transform: scale(1.02);
}

.hero-background-fade-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 0.4s ease-out, transform 0.4s ease-out;
}

.hero-background-fade-exit {
  opacity: 1;
  transform: scale(1);
}

.hero-background-fade-exit-active {
  opacity: 0;
  transform: scale(0.98);
  transition: opacity 0.3s ease-in, transform 0.3s ease-in;
}

/* Responsive optimizations */
@media (max-width: 768px) {
  .hero-background {
    background-attachment: scroll; /* Better mobile performance */
  }
}

/* High-DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .hero-background {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Reduced motion accessibility */
@media (prefers-reduced-motion: reduce) {
  .hero-background-transition {
    transition: none;
  }
  
  .hero-background-fade-enter-active,
  .hero-background-fade-exit-active {
    transition: none;
  }
}

/* Performance optimization for GPU acceleration */
.hero-background-gpu {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Critical CSS for above-the-fold content */
.hero-background-critical {
  content-visibility: auto;
  contain-intrinsic-size: 100vh;
}

/* Professional Skeleton Loading Animations */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite linear;
}

/* YouTube-style skeleton pulse */
@keyframes skeleton-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-skeleton-pulse {
  animation: skeleton-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Professional skeleton gradient */
.skeleton-gradient {
  background: linear-gradient(
    90deg,
    #f1f5f9 0%,
    #e2e8f0 25%,
    #f1f5f9 50%,
    #e2e8f0 75%,
    #f1f5f9 100%
  );
  background-size: 200% 100%;
  animation: skeleton-gradient 1.5s ease-in-out infinite;
}

@keyframes skeleton-gradient {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
