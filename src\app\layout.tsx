import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>eist, <PERSON><PERSON>st_Mono } from "next/font/google";
import "./globals.css";
import { ReduxProvider } from '@/components/providers/ReduxProvider';
import { SupabaseAuthProvider } from '@/components/providers/SupabaseAuthProvider';
import ServerSiteSettingsProvider from '@/components/providers/ServerSiteSettingsProvider';

import ChatBot from '@/components/chatbot/ChatBot';
import CloudinarySchedulerInit from '@/components/CloudinarySchedulerInit';
import ClientOnly from '@/components/ClientOnly';
// Removed complex background preloaders - using simple WordPress-style approach

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "LGU Project App - Admin Dashboard",
  description: "Admin dashboard for managing users and system operations",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {/* Critical CSS for hero background - prevents FOUC */}
        <style dangerouslySetInnerHTML={{
          __html: `
            .hero-background-critical {
              background-size: cover;
              background-position: center;
              background-repeat: no-repeat;
              transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
              will-change: background-image;
            }
          `
        }} />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ReduxProvider>
          <SupabaseAuthProvider>
            <ServerSiteSettingsProvider>
              {/* Enterprise-level background loading - no FOUC */}
              {children}
              <ClientOnly>
                <ChatBot />
                <CloudinarySchedulerInit />
              </ClientOnly>
            </ServerSiteSettingsProvider>
          </SupabaseAuthProvider>
        </ReduxProvider>
      </body>
    </html>
  );
}
