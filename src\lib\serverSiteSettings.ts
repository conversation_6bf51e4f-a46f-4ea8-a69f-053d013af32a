/**
 * Server-Side Site Settings Loader
 * 
 * Enterprise-level solution for loading site settings on the server side
 * to eliminate FOUC and provide instant background image display.
 * 
 * This is how Facebook, YouTube, and other enterprise platforms handle
 * user-customized content - they render it server-side so it's available
 * immediately without client-side fetching delays.
 */

import { createClient } from '@/utils/supabase/server'
import { SiteSettings, SiteSetting } from '@/lib/redux/slices/siteSettingsSlice'

// Default settings (same as Redux slice)
const defaultSettings: SiteSettings = {
  site_logo: {
    value: '/images/logo.png',
    cloudinary_url: null,
    cloudinary_public_id: null,
    type: 'image',
    description: 'Main site logo'
  },
  hero_background: {
    value: '/images/lgu-ipil.png',
    cloudinary_url: null,
    cloudinary_public_id: null,
    type: 'image',
    description: 'Hero background image'
  },
  site_name: {
    value: 'LGU Ipil',
    cloudinary_url: null,
    cloudinary_public_id: null,
    type: 'string',
    description: 'Site name'
  },
  site_tagline: {
    value: 'Local Government Unit',
    cloudinary_url: null,
    cloudinary_public_id: null,
    type: 'string',
    description: 'Site tagline'
  }
}

/**
 * Load site settings on the server side
 * This runs during SSR and provides immediate access to database settings
 */
export async function loadServerSiteSettings(): Promise<SiteSettings> {
  try {
    console.log('[ServerSiteSettings] Loading site settings on server...')
    
    const supabase = await createClient()

    // Get all site settings from database
    const { data: settings, error } = await supabase
      .from('site_settings')
      .select('*')
      .eq('is_active', true)
      .order('setting_key')

    if (error) {
      console.warn('[ServerSiteSettings] Database error, using defaults:', error.message)
      return defaultSettings
    }

    if (!settings || settings.length === 0) {
      console.log('[ServerSiteSettings] No settings found, using defaults')
      return defaultSettings
    }

    // Transform database results to our settings format
    const settingsMap = settings.reduce((acc: any, setting: any) => {
      acc[setting.setting_key] = {
        value: setting.setting_value,
        cloudinary_url: setting.cloudinary_url,
        cloudinary_public_id: setting.cloudinary_public_id,
        type: setting.setting_type,
        description: setting.description
      }
      return acc
    }, {})

    // 🏢 ENTERPRISE APPROACH: Complete replacement logic
    // Only use defaults for settings that don't exist in database at all
    // If a setting exists in database (even with empty values), don't merge with defaults
    const finalSettings = { ...defaultSettings }

    // Override defaults only with settings that actually exist in database
    Object.keys(settingsMap).forEach(key => {
      finalSettings[key] = settingsMap[key]
    })
    
    console.log('[ServerSiteSettings] ✅ Loaded settings:', Object.keys(finalSettings))
    
    return finalSettings

  } catch (error) {
    console.error('[ServerSiteSettings] Failed to load settings:', error)
    return defaultSettings
  }
}

/**
 * Get a specific setting URL with ENTERPRISE COMPLETE REPLACEMENT approach
 * Same logic as the client-side getSettingUrl but for server use
 */
export function getServerSettingUrl(settings: SiteSettings, key: string, fallback?: string): string {
  const setting = settings[key]

  // 🏢 ENTERPRISE APPROACH: Complete replacement - no fallback when database has value
  // If database has ANY value (cloudinary_url OR value), use it exclusively
  if (setting?.cloudinary_url || setting?.value) {
    return setting.cloudinary_url || setting.value || ''
  }

  // Only use fallback if database has NO value at all (new/empty state)
  return fallback || ''
}

/**
 * Get hero background URL specifically
 * This is the main function for eliminating FOUC on hero backgrounds
 */
export function getServerHeroBackgroundUrl(settings: SiteSettings): string {
  return getServerSettingUrl(settings, 'hero_background', '/images/lgu-ipil.png')
}
