/**
 * Database Setup API Route
 * 
 * This endpoint manually creates the site_settings table and inserts default data
 * when the standard initialization fails.
 */

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'

export async function POST() {
  try {
    const supabase = await createClient()

    // For initial setup, we'll allow this to run without authentication
    // but we'll still try to get the user for logging purposes
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    console.log('[Database Setup] User authentication status:', {
      hasUser: !!user,
      authError: authError?.message
    })

    console.log('[Database Setup] Starting manual table creation...')

    // Step 1: Try to create the table using direct table operations
    // First, check if table exists
    const { error: checkError } = await supabase
      .from('site_settings')
      .select('id')
      .limit(1)

    if (!checkError) {
      console.log('[Database Setup] Table already exists')
      return NextResponse.json({ 
        success: true, 
        message: 'Table already exists',
        action: 'verified'
      })
    }

    if (checkError.code !== '42P01') {
      console.error('[Database Setup] Unexpected error checking table:', checkError)
      return NextResponse.json({ 
        error: 'Unexpected database error',
        details: checkError
      }, { status: 500 })
    }

    console.log('[Database Setup] Table does not exist, attempting creation...')

    // Step 2: Since we can't execute raw SQL, we'll try to insert a record which should create the table
    // if it has the right schema. This won't work, but let's try a different approach.
    
    // Let's try using the service role to create the table
    try {
      const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY
      if (!serviceRoleKey) {
        return NextResponse.json({ 
          error: 'Service role key not configured',
          message: 'Cannot create table without service role access'
        }, { status: 500 })
      }

      // Import supabase-js directly for admin operations
      const { createClient: createSupabaseClient } = await import('@supabase/supabase-js')
      const supabaseAdmin = createSupabaseClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        serviceRoleKey
      )

      console.log('[Database Setup] Using admin client to create table...')

      // Try to insert default settings - this will fail if table doesn't exist
      const defaultSettings = [
        {
          setting_key: 'site_logo',
          setting_value: '/images/logo.png',
          setting_type: 'image',
          description: 'Main site logo displayed in header and sidebar',
          is_active: true
        },
        {
          setting_key: 'hero_background',
          setting_value: '/images/lgu-ipil.png',
          setting_type: 'image',
          description: 'Background image for homepage hero section',
          is_active: true
        },
        {
          setting_key: 'site_favicon',
          setting_value: null,
          setting_type: 'image',
          description: 'Browser favicon and app icon',
          is_active: true
        },
        {
          setting_key: 'site_name',
          setting_value: 'LGU Ipil',
          setting_type: 'string',
          description: 'Site name displayed in headers',
          is_active: true
        },
        {
          setting_key: 'site_tagline',
          setting_value: 'Local Gov',
          setting_type: 'string',
          description: 'Site tagline or subtitle',
          is_active: true
        },
        {
          setting_key: 'site_description',
          setting_value: 'Municipal Agriculture Office - Ipil | Personnel Management System',
          setting_type: 'string',
          description: 'Site description for meta tags',
          is_active: true
        }
      ]

      // Try to insert using admin client
      const { data: insertData, error: insertError } = await supabaseAdmin
        .from('site_settings')
        .insert(defaultSettings)
        .select()

      if (insertError) {
        console.error('[Database Setup] Insert failed:', insertError)
        return NextResponse.json({ 
          error: 'Failed to create table or insert data',
          details: insertError,
          message: 'The site_settings table needs to be created manually in the database'
        }, { status: 500 })
      }

      console.log('[Database Setup] Successfully inserted default settings:', insertData?.length)

      return NextResponse.json({ 
        success: true, 
        message: 'Database setup completed successfully',
        settings_created: insertData?.length || 0,
        action: 'created'
      })

    } catch (adminError) {
      console.error('[Database Setup] Admin operation failed:', adminError)
      return NextResponse.json({ 
        error: 'Admin database operation failed',
        details: adminError,
        message: 'Unable to create table with admin privileges'
      }, { status: 500 })
    }

  } catch (error) {
    console.error('[Database Setup] Unexpected error:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error
    }, { status: 500 })
  }
}

export async function GET() {
  try {
    const supabase = await createClient()

    // For status check, we'll allow this to run without authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    console.log('[Database Setup] Status check - User authentication:', {
      hasUser: !!user,
      authError: authError?.message
    })

    // Check table status
    const { data, error } = await supabase
      .from('site_settings')
      .select('setting_key, setting_value, created_at')
      .limit(10)

    if (error) {
      return NextResponse.json({ 
        table_exists: false,
        error: error.message,
        error_code: error.code
      })
    }

    return NextResponse.json({ 
      table_exists: true,
      settings_count: data?.length || 0,
      sample_settings: data
    })

  } catch (error) {
    console.error('[Database Setup] Status check error:', error)
    return NextResponse.json({ 
      error: 'Internal server error',
      details: error
    }, { status: 500 })
  }
}
