/**
 * Client Site Settings Initializer
 * 
 * Client component that receives server-side loaded settings
 * and immediately initializes the Redux store with them.
 * 
 * This eliminates the need for client-side API calls and
 * provides instant access to site settings.
 */

'use client'

import { useEffect, useRef } from 'react'
import { useAppDispatch } from '@/lib/store'
import { SiteSettings, siteSettingsSlice } from '@/lib/redux/slices/siteSettingsSlice'

interface ClientSiteSettingsInitializerProps {
  settings: SiteSettings
}

export default function ClientSiteSettingsInitializer({ 
  settings 
}: ClientSiteSettingsInitializerProps) {
  const dispatch = useAppDispatch()
  const initialized = useRef(false)

  useEffect(() => {
    // Only initialize once
    if (initialized.current) return
    initialized.current = true

    console.log('[ClientSiteSettingsInitializer] 🎯 Initializing Redux with server settings')

    // Directly set the settings in Redux store without API call
    dispatch(siteSettingsSlice.actions.initializeSiteSettings(settings))

    console.log('[ClientSiteSettingsInitializer] ✅ Redux store initialized with server settings')
  }, [dispatch, settings])

  // This component doesn't render anything
  return null
}
